#!/usr/bin/env python3
"""
知识库管理器
用于管理项目相关的知识库，支持向量存储和检索
"""

import os
import json
import uuid
import hashlib
import re
import io
import logging
import threading
import httpx
import atexit
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from openai import OpenAI

from pymilvus import (
    MilvusClient,
    DataType,
)
from config import Config
from knowledge_code import KnowledgeCode
from code_util import KnowledgeBase, Document
from file_converter import FileConverter
from json_chunker import JsonChunker

# 文档处理相关导入
try:
    import docx

    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    import PyPDF2

    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

# 全局字典存储项目ID与Milvus实例的映射关系
_milvus_instances = {}
_milvus_lock = threading.Lock()

def _cleanup_milvus_instances():
    """清理所有Milvus实例"""
    global _milvus_instances, _milvus_lock
    with _milvus_lock:
        # 先关闭所有Milvus连接
        for project_id, client in _milvus_instances.items():
            try:
                if hasattr(client, 'close'):
                    client.close()
                    print(f"✅ Milvus client closed for project {project_id}")
            except Exception as e:
                print(f"⚠️ Error closing Milvus client for project {project_id}: {e}")

        # 清理所有项目实例的引用
        _milvus_instances.clear()

# 注册程序退出时的清理函数
atexit.register(_cleanup_milvus_instances)

class KnowledgeManager:
    """知识库管理器"""

    def __init__(self, project, log_manager):
        self.project = project
        self.work_dir = project.work_dir
        self.data_dir = os.path.join(project.work_dir, Config.VECTOR_DB_DIR)
        self.kb_file = os.path.join(self.data_dir, "knowledge_bases.json")
        self.documents_meta_file = os.path.join(self.data_dir, "documents_meta.json")
        self.milvus_uri = os.path.join(self.data_dir, "milvus.db")
        self.documents_dir = os.path.join(self.data_dir, "documents")

        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        # os.makedirs(os.path.dirname(self.milvus_uri), exist_ok=True)
        os.makedirs(self.documents_dir, exist_ok=True)

        # 加载知识库数据
        self.knowledge_bases = self._load_knowledge_bases()
        self.documents_meta = self._load_documents_meta()

        # 初始化embedding配置
        self.embedding_model = Config.EMBEDDING_MODEL
        self.embedding_client = None
        self._init_milvus()

        # 初始化代码知识库管理器
        self.knowledge_code = None
        self._init_knowledge_code()

        # 初始化文件转换器
        self.file_converter = FileConverter()

    def __del__(self):
        """析构函数，确保资源正确清理"""
        try:
            # 注意：不要在这里关闭milvus_client，因为它可能被其他实例共享
            # 只清理embedding_client的http_client
            if hasattr(self, 'embedding_client') and self.embedding_client:
                if hasattr(self.embedding_client, '_client') and hasattr(self.embedding_client._client, 'close'):
                    self.embedding_client._client.close()
        except Exception:
            # 忽略析构函数中的异常
            pass

    def _init_milvus(self):
        """初始化Milvus连接"""
        try:
            # 初始化embedding客户端，支持SSL验证配置
            embedding_kwargs = {
                "api_key": Config.EMBEDDING_API_KEY,
                "base_url": Config.EMBEDDING_BASE_URL
            }

            # 检查是否需要禁用SSL验证
            embedding_verify_ssl = os.getenv('EMBEDDING_VERIFY_SSL',"").lower() == 'true'
            if not embedding_verify_ssl:
                http_client = httpx.Client(verify=False)
                embedding_kwargs["http_client"] = http_client

            self.embedding_client = OpenAI(**embedding_kwargs)
            print("✅ embedding模型初始化成功")

            # 使用全局字典确保每个项目ID对应唯一的Milvus实例
            global _milvus_instances, _milvus_lock
            with _milvus_lock:
                # 检查是否在Werkzeug重载器中，如果是，则清理可能存在的旧实例
                if os.environ.get('WERKZEUG_RUN_MAIN') == 'true':
                    if self.project.project_id in _milvus_instances:
                        try:
                            old_client = _milvus_instances[self.project.project_id]
                            if hasattr(old_client, 'close'):
                                old_client.close()
                                print(f"✅ Closed old Milvus client for project {self.project.project_id}")
                        except Exception as e:
                            print(f"⚠️ Error closing old Milvus client: {e}")
                        del _milvus_instances[self.project.project_id]

                # 只有当该项目ID没有实例时才创建新实例
                if self.project.project_id not in _milvus_instances:
                    _milvus_instances[self.project.project_id] = MilvusClient(self.milvus_uri)
                    print(f"✅ Milvus Lite initialized at {self.milvus_uri} for project {self.project.project_id}")

                self.milvus_client = _milvus_instances[self.project.project_id]

        except Exception as e:
            self.milvus_client = None
            raise Exception(f"⚠️ Failed to initialize Milvus: {e}")

    def _init_knowledge_code(self):
        """初始化代码知识库管理器"""
        try:
            if self.embedding_client and self.milvus_client:
                self.knowledge_code = KnowledgeCode(
                    project=self.project,
                    embedding_client=self.embedding_client,
                    milvus_client=self.milvus_client
                )
                print("✅ 代码知识库管理器初始化成功")
            else:
                print("⚠️ 无法初始化代码知识库管理器：embedding_client或milvus_client未初始化")
        except Exception as e:
            print(f"⚠️ 代码知识库管理器初始化失败: {e}")
            self.knowledge_code = None

    def _generate_embedding(self, text: str) -> List[float]:
        """生成文本的向量表示"""
        try:
            # 使用embedding模型
            response = self.embedding_client.embeddings.create(
                model=self.embedding_model, input=text
            )
            return response.data[0].embedding
        except Exception as e:
            raise Exception(f"⚠️ embedding失败: {e}")

        return []

    def _split_text(self, text: str, use_markdown_separator: bool = True) -> List[str]:
        """分割文本为块"""
        chunk_size = Config.CHUNK_SIZE
        chunk_overlap = Config.CHUNK_OVERLAP

        # 获取markdown分隔符配置
        markdown_separator = Config.MARKDOWN_SEPARATOR

        # 将文本按行分割
        lines = text.split('\n')
        
        if use_markdown_separator and markdown_separator:
            # 尝试按markdown分隔符分块
            markdown_chunks = self._split_by_markdown_separator(
                text, markdown_separator
            )
            if markdown_chunks and len(markdown_chunks) > 1:
                # 如果成功按markdown分隔符分块，进一步处理每个块
                final_chunks = []
                for chunk in markdown_chunks:
                    chunk_lines = chunk.split('\n')
                    if len(chunk_lines) <= chunk_size:
                        final_chunks.append(chunk)
                    else:
                        # 如果单个markdown块太大，继续按行数分割
                        sub_chunks = self._split_lines_by_size(
                            chunk_lines, chunk_size, chunk_overlap
                        )
                        # 将行列表转换为文本
                        sub_chunks_text = ['\n'.join(sub_chunk) for sub_chunk in sub_chunks]
                        final_chunks.extend(sub_chunks_text)
                return final_chunks

        # 如果没有markdown分隔符或分块失败，按行数分割
        lines_chunks = self._split_lines_by_size(lines, chunk_size, chunk_overlap)
        # 将行列表转换为文本
        return ['\n'.join(chunk_lines) for chunk_lines in lines_chunks]

    def _split_by_markdown_separator(self, text: str, separator: str) -> List[str]:
        """按markdown分隔符分割文本"""
        chunks = []
        lines = text.split("\n")
        current_chunk = []

        # 处理不同类型的分隔符
        if separator.startswith("#"):
            # Markdown标题分隔符
            separator_pattern = separator + " "
        else:
            # 其他分隔符（如 ---）
            separator_pattern = separator

        for line in lines:
            line_stripped = line.strip()

            # 检查是否是分隔符
            is_separator = False
            if separator.startswith("#"):
                # 对于Markdown标题，检查是否以指定级别的#开头，并且后面跟着空格和章节编号
                if line_stripped.startswith(separator_pattern):
                    # 进一步检查是否有章节编号（如：### 6.4.2. 监控组件功能扩展）
                    remaining_text = line_stripped[len(separator_pattern) :].strip()
                    if remaining_text:
                        # 检查是否以数字开头（章节编号）
                        # 匹配章节编号模式：数字.数字.数字. 或 数字. 等
                        if re.match(r"^\d+(\.\d+)*\.?\s+", remaining_text):
                            is_separator = True
                        # 如果没有章节编号，但有内容，也认为是章节标题
                        elif remaining_text and not remaining_text.startswith("#"):
                            is_separator = True
            else:
                # 对于其他分隔符，检查是否完全匹配或包含分隔符
                is_separator = (
                    line_stripped == separator
                    or line_stripped.startswith(separator)
                    or separator in line_stripped
                )

            if is_separator and current_chunk:
                # 保存当前块
                chunk_text = "\n".join(current_chunk).strip()
                if chunk_text:
                    chunks.append(chunk_text)
                current_chunk = [line]  # 开始新块，包含分隔符行
            else:
                current_chunk.append(line)

        # 保存最后一个块
        if current_chunk:
            chunk_text = "\n".join(current_chunk).strip()
            if chunk_text:
                chunks.append(chunk_text)

        return chunks

    def _split_lines_by_size(
        self, lines: List[str], chunk_size: int, chunk_overlap: int
    ) -> List[List[str]]:
        """按行数分割文本行"""
        if len(lines) <= chunk_size:
            return [lines]

        chunks = []
        start = 0

        while start < len(lines):
            end = start + chunk_size
            chunk_lines = lines[start:end]
            
            # 只有在不是第一个块的情况下才添加重叠部分
            if start > 0 and chunk_overlap > 0:
                # 添加重叠行到当前块的开头
                overlap_start = max(0, start - chunk_overlap)
                overlap_lines = lines[overlap_start:start]
                chunk_lines = overlap_lines + chunk_lines

            if chunk_lines:  # 确保块不为空
                chunks.append(chunk_lines)

            start = end
            if start >= len(lines):
                break

        return chunks

    def _split_by_size(
        self, text: str, chunk_size: int, chunk_overlap: int
    ) -> List[str]:
        """按大小分割文本（已废弃，保留以确保向后兼容）"""
        lines = text.split('\n')
        line_chunks = self._split_lines_by_size(lines, chunk_size, chunk_overlap)
        return ['\n'.join(chunk_lines) for chunk_lines in line_chunks]

    def _load_knowledge_bases(self) -> Dict[str, KnowledgeBase]:
        """加载知识库数据"""
        if os.path.exists(self.kb_file):
            try:
                with open(self.kb_file, "r", encoding="utf-8") as f:
                    kb_data = json.load(f)
                    # 支持多个知识库
                    knowledge_bases = {}
                    if isinstance(kb_data, dict):
                        if "kb_id" in kb_data:
                            # 兼容旧格式：单个知识库
                            kb = KnowledgeBase(**kb_data)
                            knowledge_bases[kb.kb_id] = kb
                        else:
                            # 新格式：多个知识库
                            for kb_id, kb_info in kb_data.items():
                                knowledge_bases[kb_id] = KnowledgeBase(**kb_info)
                    return knowledge_bases
            except Exception as e:
                print(f"Error loading knowledge bases: {e}")
        return {}

    def _save_knowledge_bases(self):
        """保存知识库数据"""
        try:
            with open(self.kb_file, "w", encoding="utf-8") as f:
                if self.knowledge_bases:
                    # 保存多个知识库
                    kb_data = {}
                    for kb_id, kb in self.knowledge_bases.items():
                        kb_data[kb_id] = kb.to_dict()
                    json.dump(kb_data, f, ensure_ascii=False, indent=2)
                else:
                    json.dump({}, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving knowledge bases: {e}")

    def _load_documents_meta(self) -> Dict[str, Any]:
        """加载文档元数据"""
        if os.path.exists(self.documents_meta_file):
            try:
                with open(self.documents_meta_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading documents meta: {e}")
        return {}

    def _save_documents_meta(self):
        """保存文档元数据"""
        try:
            with open(self.documents_meta_file, "w", encoding="utf-8") as f:
                json.dump(self.documents_meta, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving documents meta: {e}")

    def get_or_create_knowledge_base(self, kb_id: str = "kb_code") -> KnowledgeBase:
        """获取或创建知识库"""
        if kb_id in self.knowledge_bases:
            return self.knowledge_bases[kb_id]

        # 创建新的知识库
        collection_name = kb_id

        # 根据kb_id确定知识库名称和描述
        if kb_id == "kb_code":
            name = f"{self.project.name} 的代码知识库"
            description = "这是项目的代码知识库，用于存储和检索代码片段"
        elif kb_id == "kb_documents":
            name = f"{self.project.name} 的文档知识库"
            description = "这是项目的文档知识库，用于存储和检索文档内容"
        else:
            name = f"{self.project.name} 的{kb_id}知识库"
            description = f"这是项目的{kb_id}知识库"

        # 创建知识库对象
        kb = KnowledgeBase(
            kb_id=kb_id,
            name=name,
            description=description,
            project_id=self.project.project_id,
            collection_name=collection_name,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat(),
        )

        # 创建Milvus集合
        if self.milvus_client and self._create_collection(collection_name):
            self.knowledge_bases[kb_id] = kb
            self._save_knowledge_bases()
            return kb
        else:
            raise Exception("Failed to create Milvus collection")

    def _create_collection(self, collection_name: str) -> bool:
        """创建Milvus集合"""
        try:
            if not self.milvus_client:
                return False

            # 检查集合是否已存在
            if self.milvus_client.has_collection(collection_name):
                print(f"Collection {collection_name} already exists")
                return True

            # 使用MilvusClient的简化API创建集合
            self.milvus_client.create_collection(
                collection_name=collection_name,
                dimension=Config.EMBEDDING_DIMENSION,
                primary_field_name="id",
                id_type="string",
                vector_field_name="embedding",
                metric_type="L2",
                auto_id=False,
                max_length=100,  # 主键字段最大长度
            )

            print(f"✅ Collection {collection_name} created successfully")
            return True
        except Exception as e:
            print(f"Error creating collection: {e}")
            raise e

    def delete_knowledge_base(self, kb_id: str = "kb_documents") -> bool:
        """删除指定知识库"""
        if kb_id not in self.knowledge_bases:
            return False

        try:
            kb = self.knowledge_bases[kb_id]
            # 删除Milvus集合
            if self.milvus_client and self.milvus_client.has_collection(kb.collection_name):
                self.milvus_client.drop_collection(kb.collection_name)
                print(f"✅ Collection {kb.collection_name} dropped")

            # 从知识库字典中删除
            del self.knowledge_bases[kb_id]
            self._save_knowledge_bases()

            return True
        except Exception as e:
            print(f"Error deleting knowledge base: {e}")
            return False

    def save_document_file(self, file_obj, original_filename: str, additional_meta: Dict[str, Any] = None) -> str:
        """保存原始文档文件到documents目录"""
        try:
            # 确保documents目录存在
            os.makedirs(self.documents_dir, exist_ok=True)

            # 使用原始文件名保存文件
            file_path = os.path.join(self.documents_dir, original_filename)

            # 保存文件
            file_obj.seek(0)  # 重置文件指针
            with open(file_path, "wb") as f:
                f.write(file_obj.read())

            print(f"✅ 原始文档已保存: {file_path}")

            # 保存文档元数据
            # 如果存在同名文档，先删除原来的文档
            if original_filename in self.documents_meta:
                print(f"🔄 检测到同名文档 {original_filename}，将删除原文档")
                self.delete_document(original_filename)
            
            # 基础元数据
            base_meta = {
                "name": original_filename,
                "title": original_filename,
                "file_path": file_path,
                "status": "processing",
                "size": os.path.getsize(file_path),
                "type": os.path.splitext(original_filename)[1],
                "chunk_count": 0,
            }

            # 合并额外的元数据
            if additional_meta:
                base_meta.update(additional_meta)

            self.documents_meta[original_filename] = base_meta
            self._save_documents_meta()

            return file_path
        except Exception as e:
            print(f"⚠️ 保存原始文档失败: {e}")
            return ""

    def process_document(self, doc_name):
        doc_meta = self.get_document(doc_name)
        doc_name = doc_meta.get("name")
        file_path = doc_meta.get("file_path")
        # 后台处理文档内容和向量化
        def process_document():
            try:
                # 提取文件内容
                content = extract_file_content(file_path, self.file_converter)
                if content:
                    doc_meta["status"] = "processing"
                    # 添加到知识库
                    meta = self.add_document(
                        doc_meta, content
                    )
                else:
                    doc_meta["status"] = "failed"
                    logging.error(f"无法提取文件内容: {doc_name}")
            except Exception as e:
                doc_meta["status"] = "failed"
                logging.error(f"后台处理文档失败: {e}")
            self._save_documents_meta()

        # 启动后台线程处理
        thread = threading.Thread(target=process_document)
        thread.daemon = True
        thread.start()

    def process_document_by_metadata(self, doc_meta, is_rebuild: bool = False):
        """直接使用提供的文档元数据处理文档（用于重建功能）

        Args:
            doc_meta: 文档元数据
            is_rebuild: 是否为重建操作，True时重建完成后需要增加统计
        """
        doc_name = doc_meta.get("name")
        file_path = doc_meta.get("file_path")

        # 后台处理文档内容和向量化
        def process_document():
            try:
                # 提取文件内容
                content = extract_file_content(file_path, self.file_converter)
                if content:
                    doc_meta["status"] = "processing"
                    # 重新添加文档元数据（用于后台处理）
                    self.documents_meta[doc_name] = doc_meta
                    self._save_documents_meta()

                    # 添加到知识库
                    meta = self.add_document(
                        doc_meta, content, is_rebuild=is_rebuild
                    )
                else:
                    doc_meta["status"] = "failed"
                    logging.error(f"无法提取文件内容: {doc_name}")
            except Exception as e:
                doc_meta["status"] = "failed"
                logging.error(f"后台处理文档失败: {e}")
            finally:
                self._save_documents_meta()

        # 启动后台线程处理
        thread = threading.Thread(target=process_document)
        thread.daemon = True
        thread.start()

    def add_document(
        self, doc_meta: Dict[str, Any], content: str, kb_id: str = "kb_documents", is_rebuild: bool = False
    ) -> Dict[str, Any]:
        """添加文档到知识库（支持分块）

        Args:
            doc_meta: 文档元数据
            content: 文档内容
            kb_id: 知识库ID
            is_rebuild: 是否为重建操作，重建时只增加统计不减少
        """
        kb = self.get_or_create_knowledge_base(kb_id)
        if not kb:
            raise Exception("Knowledge base not found")

        # 使用文件名作为文档ID
        doc_name = doc_meta.get("name")

        # 如果存在同名文档，先删除原来的文档（重建时跳过这个检查）
        if not is_rebuild:
            # 这里检查文档是否已经存在于知识库中（基于Milvus中的数据）
            try:
                search_result = self.milvus_client.query(
                    collection_name=kb.collection_name,
                    filter=f"metadata[\"document_name\"] == '{doc_name}'",
                    output_fields=["id"]
                )
                if search_result and len(search_result) > 0:
                    print(f"🔄 检测到知识库中存在同名文档 {doc_name}，将删除原文档")
                    self.delete_document(doc_name)
            except Exception as e:
                print(f"⚠️ 检查同名文档时出错: {e}")

        try:
            # 检查是否为JSON文档
            if doc_name.lower().endswith('.json'):
                # 检查文档元数据中是否指定了JSON处理选项
                json_processing = doc_meta.get("metadata", {}).get("json_processing", {})
                smart_chunking = json_processing.get("smart_chunking", True)  # 默认启用智能分块

                if smart_chunking:
                    # 使用JSON切块器处理JSON文档（递归分层分块）
                    json_chunker = JsonChunker(json_processing)
                    chunks_data = json_chunker.chunk_json_document(content, doc_name)
                    chunks = [chunk["content"] for chunk in chunks_data]
                    chunk_metadata_list = chunks_data  # 保存完整的块数据供后面使用

                    # 获取JSON摘要信息并添加到文档元数据
                    json_summary = json_chunker.get_json_summary(content)
                    if json_summary:
                        # 保存JSON摘要信息到文档元数据
                        doc_meta["json_summary"] = json_summary

                    logging.debug(f"✅ JSON文档 {doc_name} 使用递归分层分块，生成 {len(chunks)} 个块")
                    if json_summary and json_summary.get("structure_type"):
                        logging.debug(f"   📋 JSON结构: {json_summary['structure_type']}, 根节点数: {json_summary.get('total_elements', 0)}")
                else:
                    # 使用传统文本分块处理JSON文档
                    chunks = self._split_text(content)
                    chunk_metadata_list = None
                    logging.debug(f"✅ JSON文档 {doc_name} 使用传统分块，生成 {len(chunks)} 个块")
            else:
                # 分块处理文档内容（传统文本分块）
                chunks = self._split_text(content)
                chunk_metadata_list = None

            # 为每个块生成向量并存储
            chunk_ids = []
            for i, chunk in enumerate(chunks):
                if chunk.strip():  # 跳过空块
                    chunk_id = f"{doc_name}_chunk_{i}"
                    chunk_ids.append(chunk_id)

                    # 生成块向量
                    embedding = self._generate_embedding(chunk)
                    if not embedding:
                        continue

                    # 准备块元数据
                    if chunk_metadata_list and i < len(chunk_metadata_list):
                        # 使用JSON切块器生成的元数据
                        chunk_metadata = chunk_metadata_list[i]["metadata"].copy()
                        chunk_metadata.update({
                            "document_name": doc_name,
                            "chunk_index": i,
                            "chunk_type": "json",
                        })
                    else:
                        # 默认文本块元数据
                        chunk_metadata = {
                            "document_name": doc_name,
                            "chunk_index": i,
                            "chunk_type": "text",
                        }

                    # 准备数据 - 使用MilvusClient的简化格式
                    data = [
                        {
                            "id": chunk_id,
                            "title": doc_name, # 用章节标题代替
                            "content": chunk,
                            "embedding": embedding,
                            "metadata": json.dumps(chunk_metadata, ensure_ascii=False),
                        }
                    ]

                    # 插入到Milvus
                    self.milvus_client.insert(
                        collection_name=kb.collection_name, data=data
                    )

            # 更新知识库统计
            # 重建时只增加，不重复增加文档计数（因为删除时已经减少了）
            if is_rebuild:
                # 重建时：只增加分块数量，文档数量已在删除时减去了
                kb.chunk_count += len(chunks)  # 只更新块数
                kb.document_count += 1  # 恢复文档数量
            else:
                # 新增时：同时增加文档和分块数量
                kb.document_count += 1
                kb.chunk_count += len(chunks)  # 更新总块数

            kb.updated_at = datetime.now().isoformat()
            self._save_knowledge_bases()

            # 更新文档元数据状态为completed
            doc_meta["status"] = "completed"
            doc_meta["chunk_count"] = len(chunks)

            logging.debug(f"✅ 文档已添加到知识库: {doc_name} (共{len(chunks)}个块)")
            return doc_meta
        except Exception as e:
            # 更新文档元数据状态为failed
            doc_meta["status"] = "failed"
            raise Exception(f"Milvus storage failed: {e}")
        finally:
            self._save_documents_meta()

    def search_documents(self, query: str, limit: int = 10, kb_id: str = "kb_documents") -> List[Dict[str, Any]]:
        """在知识库中搜索文档"""
        if kb_id not in self.knowledge_bases:
            return []
        kb = self.knowledge_bases[kb_id]

        try:
            if not self.milvus_client or not self.milvus_client.has_collection(kb.collection_name):
                return []
            # 生成查询向量
            query_embedding = self._generate_embedding(query)
            if not query_embedding:
                return []

            # 使用MilvusClient的简化搜索API
            results = self.milvus_client.search(
                collection_name=kb.collection_name,
                data=[query_embedding],
                limit=limit,
                output_fields=["id", "title", "content", "metadata"],
            )

            # 格式化结果
            documents = []
            for result_list in results:
                for result in result_list:
                    entity_data = result["entity"]
                    # 解析元数据
                    metadata = json.loads(entity_data.get("metadata", "{}"))
                    document_name = metadata.get("document_name")
                    
                    # 文档知识库检查文档是否仍然存在于元数据中（未被删除）
                    if kb_id == "kb_code" or (document_name and document_name in self.documents_meta):
                        documents.append(
                            {
                                "id": entity_data.get("id"),
                                "title": entity_data.get("title"),
                                "content": entity_data.get("content"),
                                "metadata": metadata,
                                "score": result["distance"],
                            }
                        )

            return documents

        except Exception as e:
            print(f"⚠️ Milvus search failed: {e}")
            return []

    def get_documents(self) -> List[Dict[str, Any]]:
        """获取知识库中的所有文档元数据"""
        # 返回存储在documents_meta.json中的文档元数据
        documents = []
        for doc_name, meta in self.documents_meta.items():
            documents.append(meta)
        return documents
    def get_document(self, doc_name) -> Dict[str, Any]:
        """获取指定文档元数据"""
        # 返回存储在documents_meta.json中的文档元数据
        meta = self.documents_meta.get(doc_name)
        return meta

    def delete_document(self, filename: str, kb_id: str = "kb_documents", delete_physical_file: bool = True, update_kb_stats: bool = True) -> bool:
        """删除知识库中的单个文档

        Args:
            filename: 文档文件名
            kb_id: 知识库ID
            delete_physical_file: 是否删除物理文件，True为删除，False为只删除向量库数据
            update_kb_stats: 是否更新知识库统计，True为更新，False为不更新（用于重建）
        """
        if kb_id not in self.knowledge_bases:
            return False
        kb = self.knowledge_bases[kb_id]

        try:
            # 获取要删除的文档的块数量
            doc_meta = self.get_document(filename)
            chunk_count_to_remove = doc_meta.get("chunk_count", 0) if doc_meta else 0

            # 删除文档 - 使用更严格的过滤条件
            delete_expr = f"metadata[\"document_name\"] == \"{filename}\""
            self.milvus_client.delete(
                collection_name=kb.collection_name,
                filter=delete_expr,
            )

            # 只有在需要更新知识库统计时才更新
            if update_kb_stats:
                # 更新知识库统计
                if kb.document_count > 0:
                    kb.document_count -= 1
                    kb.chunk_count -= chunk_count_to_remove  # 更新总块数
                    kb.updated_at = datetime.now().isoformat()
                    self._save_knowledge_bases()

            # 删除文档元数据
            if filename in self.documents_meta:
                del self.documents_meta[filename]
                self._save_documents_meta()

            # 只有在指定要删除物理文件时才删除物理文件
            if delete_physical_file and kb_id == "kb_documents":
                try:
                    # 删除原始文件
                    file_path = os.path.join(self.documents_dir, filename)
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        print(f"✅ Document file deleted: {file_path}")

                    # 删除转换后的markdown文件（如果存在）
                    name, ext = os.path.splitext(filename)
                    if ext.lower() in ['.pdf', '.docx']:
                        markdown_filename = name + ".md"
                        markdown_path = os.path.join(self.documents_dir, markdown_filename)
                        if os.path.exists(markdown_path):
                            os.remove(markdown_path)
                            print(f"✅ Converted markdown file deleted: {markdown_path}")
                except Exception as e:
                    print(f"⚠️ Error deleting document file {filename}: {e}")

            return True
        except Exception as e:
            print(f"⚠️ Milvus delete failed: {e}")
            return False

    def delete_document_with_stats(self, filename: str, chunk_count: int, kb_id: str = "kb_documents", delete_physical_file: bool = True) -> bool:
        """删除知识库中的单个文档，并使用提供的分块数量更新统计

        Args:
            filename: 文档文件名
            chunk_count: 要删除的分块数量
            kb_id: 知识库ID
            delete_physical_file: 是否删除物理文件，True为删除，False为只删除向量库数据
        """
        if kb_id not in self.knowledge_bases:
            return False
        kb = self.knowledge_bases[kb_id]

        try:
            # 删除文档 - 使用更严格的过滤条件
            delete_expr = f"metadata[\"document_name\"] == \"{filename}\""
            self.milvus_client.delete(
                collection_name=kb.collection_name,
                filter=delete_expr,
            )

            # 更新知识库统计
            if kb.document_count > 0:
                kb.document_count -= 1
                kb.chunk_count -= chunk_count  # 使用提供的分块数量
                kb.updated_at = datetime.now().isoformat()
                self._save_knowledge_bases()

            # 删除文档元数据
            if filename in self.documents_meta:
                del self.documents_meta[filename]
                self._save_documents_meta()

            # 只有在指定要删除物理文件时才删除物理文件
            if delete_physical_file and kb_id == "kb_documents":
                try:
                    # 删除原始文件
                    file_path = os.path.join(self.documents_dir, filename)
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        print(f"✅ Document file deleted: {file_path}")

                    # 删除转换后的markdown文件（如果存在）
                    name, ext = os.path.splitext(filename)
                    if ext.lower() in ['.pdf', '.docx']:
                        markdown_filename = name + ".md"
                        markdown_path = os.path.join(self.documents_dir, markdown_filename)
                        if os.path.exists(markdown_path):
                            os.remove(markdown_path)
                            print(f"✅ Converted markdown file deleted: {markdown_path}")
                except Exception as e:
                    print(f"⚠️ Error deleting document file {filename}: {e}")

            return True
        except Exception as e:
            print(f"⚠️ Milvus delete failed: {e}")
            return False

    def clear_knowledge_base(self, kb_id: str = "kb_documents") -> bool:
        """清空知识库中的所有文档

        注意：需求文档(requirement.md)和设计文档(design.md)会被保留
        """
        if kb_id not in self.knowledge_bases:
            return False
        kb = self.knowledge_bases[kb_id]

        # 保护的文档列表（需求和设计文档）
        protected_docs = ["requirement.md", "design.md"]

        try:
            # 如果是文档知识库，先保存需求和设计文档的元数据
            protected_meta = {}
            if kb_id == "kb_documents":
                for doc_name in protected_docs:
                    if doc_name in self.documents_meta:
                        protected_meta[doc_name] = self.documents_meta[doc_name].copy()
                        # 获取该文档的分块数量，用于后续更新统计
                        chunk_count = protected_meta[doc_name].get("chunk_count", 0)
                        # 从即将清空的统计中排除受保护文档
                        if chunk_count > 0:
                            kb.chunk_count -= chunk_count
                            kb.document_count -= 1

            # 使用MilvusClient删除所有非保护文档
            if self.milvus_client and self.milvus_client.has_collection(
                kb.collection_name
            ):
                if kb_id == "kb_documents" and protected_meta:
                    # 对于文档知识库，删除除了受保护文档之外的所有文档
                    for doc_name in list(self.documents_meta.keys()):
                        if doc_name not in protected_docs:
                            # 删除单个文档
                            delete_expr = f"metadata[\"document_name\"] == \"{doc_name}\""
                            self.milvus_client.delete(
                                collection_name=kb.collection_name,
                                filter=delete_expr,
                            )
                    print(f"✅ All documents cleared from {kb.collection_name} (保留需求和设计文档)")
                else:
                    # 其他知识库删除所有记录
                    self.milvus_client.delete(
                        collection_name=kb.collection_name,
                        filter="id != ''",  # 匹配所有记录
                    )
                    print(f"✅ All documents cleared from {kb.collection_name}")

            # 更新知识库统计
            kb.document_count = len(protected_meta)  # 只保留受保护文档的数量
            kb.chunk_count = sum(meta.get("chunk_count", 0) for meta in protected_meta.values())
            kb.updated_at = datetime.now().isoformat()
            self._save_knowledge_bases()

            # 更新文档元数据，只保留受保护的文档
            if kb_id == "kb_documents" and protected_meta:
                self.documents_meta = protected_meta
            else:
                self.documents_meta = {}
            self._save_documents_meta()

            # 如果是文档知识库，删除documents目录下的所有物理文件（包括原始文件和转换后的markdown文件）
            # 但保留需求和设计文档
            if kb_id == "kb_documents":
                try:
                    # 删除documents目录下的非保护文件
                    if os.path.exists(self.documents_dir):
                        for filename in os.listdir(self.documents_dir):
                            if filename not in protected_docs:
                                file_path = os.path.join(self.documents_dir, filename)
                                if os.path.isfile(file_path):
                                    os.remove(file_path)
                        print(f"✅ All document files deleted from {self.documents_dir} (保留需求和设计文档)")
                except Exception as e:
                    print(f"⚠️ Error deleting document files: {e}")

            return True
        except Exception as e:
            print(f"⚠️ Milvus clear failed: {e}")
            return False

    # 代码知识库相关方法
    def build_code_knowledge_base(self) -> Dict[str, Any]:
        """构建代码知识库"""
        if not self.knowledge_code:
            return {
                "success": False,
                "error": "代码知识库管理器未初始化",
                "processed_files": 0,
                "total_chunks": 0
            }

        try:
            # 确保kb_code知识库存在
            kb = self.get_or_create_knowledge_base("kb_code")
            
            # 重置计数器
            kb.document_count = 0
            kb.chunk_count = 0
            
            def build_progress(processed_file: str, total_chunks: int) -> None:
                """处理进度回调函数"""
                logging.info(f"已处理文件 {processed_file} , 共 {total_chunks} 个代码块.")
                kb.document_count += 1
                kb.chunk_count = total_chunks
                kb.updated_at = datetime.now().isoformat()

            # 构建代码知识库，传入KnowledgeBase实例
            result = self.knowledge_code.build(kb, build_progress)

            # 更新知识库统计信息
            if result.get("success"):
                kb.document_count = result.get("processed_files", 0)
                kb.chunk_count = result.get("total_chunks", 0)
                kb.updated_at = datetime.now().isoformat()
                self._save_knowledge_bases()

            return result
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "processed_files": 0,
                "total_chunks": 0
            }

    def rebuild_code_knowledge_base(self) -> Dict[str, Any]:
        """重建代码知识库"""
        if not self.knowledge_code:
            return {
                "success": False,
                "error": "代码知识库管理器未初始化",
                "processed_files": 0,
                "total_chunks": 0
            }

        try:
            # 确保kb_code知识库存在
            kb = self.get_or_create_knowledge_base("kb_code")
                
            if not self.knowledge_code._clear_code_knowledge_base(kb.collection_name):
                raise Exception(" 代码库清理失败.")
            # 更新知识库统计
            kb.document_count = 0
            kb.chunk_count = 0  # 重置总块数
            kb.updated_at = datetime.now().isoformat()
            self._save_knowledge_bases()
            
            logging.info("✅ 代码库清理完成，开始重新构建...")
            
            def build_progress(processed_file: str, total_chunks: int) -> None:
                """处理进度回调函数"""
                logging.info(f"已处理文件 {processed_file} , 共 {total_chunks} 个代码块.")
                kb.document_count += 1
                kb.chunk_count = total_chunks
                kb.updated_at = datetime.now().isoformat()

            # 构建代码知识库，传入KnowledgeBase实例
            result = self.knowledge_code.build(kb, build_progress)

            # 更新知识库统计信息
            if result.get("success"):
                kb.document_count = result.get("processed_files", 0)
                kb.chunk_count = result.get("total_chunks", 0)
                kb.updated_at = datetime.now().isoformat()
                self._save_knowledge_bases()

            return result
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "processed_files": 0,
                "total_chunks": 0
            }

    def search_code(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """搜索代码知识库"""
        return self.search_documents(query, limit, "kb_code")

    def clear_code_knowledge_base(self) -> bool:
        """清空代码知识库"""
        return self.clear_knowledge_base("kb_code")


def extract_file_content(file_path_or_obj, file_converter=None):
    """提取文件内容"""
    try:
        # 处理上传的文件对象 (Flask FileStorage)
        if hasattr(file_path_or_obj, "filename") and hasattr(file_path_or_obj, "read"):
            filename = file_path_or_obj.filename
            _, ext = os.path.splitext(filename)

            # 读取文件内容
            file_content = file_path_or_obj.read()
            # 重置文件指针，以便后续可能的再次读取
            file_path_or_obj.seek(0)

            # 根据文件扩展名处理内容
            if ext.lower() in [".txt", ".md", ".csv"]:
                return file_content.decode("utf-8")
            elif ext.lower() == ".json":
                try:
                    data = json.loads(file_content)
                    return json.dumps(data, ensure_ascii=False, indent=2)
                except json.JSONDecodeError as e:
                    logging.error(f"JSON解析失败: {e}")
                    return file_content.decode("utf-8")
            elif ext.lower() == ".pdf" or ext.lower() == ".docx":
                # 对于PDF和Word文件，使用FileConverter
                if file_converter:
                    # 保存临时文件
                    import tempfile
                    with tempfile.NamedTemporaryFile(delete=False, suffix=ext) as temp_file:
                        temp_file.write(file_content)
                        temp_file_path = temp_file.name

                    try:
                        # 调用doc_to_markdown方法
                        markdown_content = file_converter.doc_to_markdown(temp_file_path)
                        return markdown_content
                    finally:
                        # 清理临时文件
                        if os.path.exists(temp_file_path):
                            os.unlink(temp_file_path)
                else:
                    # 回退到原始处理方式
                    if ext.lower() == ".pdf" and PDF_AVAILABLE:
                        pdf_reader = PyPDF2.PdfReader(io.BytesIO(file_content))
                        text = ""
                        for page in pdf_reader.pages:
                            text += page.extract_text() + "\n"
                        return text.strip()
                    elif ext.lower() == ".docx" and DOCX_AVAILABLE:
                        doc = docx.Document(io.BytesIO(file_content))
                        text = ""
                        for paragraph in doc.paragraphs:
                            text += paragraph.text + "\n"
                        return text.strip()
            else:
                # 尝试以文本方式解码
                try:
                    return file_content.decode("utf-8")
                except UnicodeDecodeError:
                    try:
                        return file_content.decode("gbk")
                    except UnicodeDecodeError:
                        return None
        else:
            # 处理文件路径 (原始逻辑)
            file_path = file_path_or_obj
            if not os.path.exists(file_path):
                return None

            _, ext = os.path.splitext(file_path)
            if ext.lower() in [".txt", ".md", ".csv"]:
                with open(file_path, "r", encoding="utf-8") as f:
                    return f.read()
            elif ext.lower() == ".json":
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        data = json.load(f)
                        return json.dumps(data, ensure_ascii=False, indent=2)
                except json.JSONDecodeError as e:
                    logging.error(f"JSON解析失败: {e}")
                    with open(file_path, "r", encoding="utf-8") as f:
                        return f.read()
            elif ext.lower() == ".pdf" or ext.lower() == ".docx":
                # 对于PDF和Word文件，使用FileConverter
                if file_converter:
                    return file_converter.doc_to_markdown(file_path)
                else:
                    # 回退到原始处理方式
                    if ext.lower() == ".pdf" and PDF_AVAILABLE:
                        with open(file_path, "rb") as f:
                            pdf_reader = PyPDF2.PdfReader(f)
                            text = ""
                            for page in pdf_reader.pages:
                                text += page.extract_text() + "\n"
                            return text.strip()
                    elif ext.lower() == ".docx" and DOCX_AVAILABLE:
                        with open(file_path, "rb") as f:
                            doc = docx.Document(f)
                            text = ""
                            for paragraph in doc.paragraphs:
                                text += paragraph.text + "\n"
                            return text.strip()
            else:
                return None

    except Exception as e:
        logging.error(f"提取文件内容失败: {e}")
        return None
