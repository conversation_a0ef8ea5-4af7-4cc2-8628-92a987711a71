#!/usr/bin/env python3
"""
xterm终端管理模块
"""

import os
import sys
import json
import threading
import logging
import io
import pty
import select
import termios
import struct
import fcntl
import time
import signal
import tempfile
import gevent
import argparse

from datetime import datetime
from flask import Flask, request, send_from_directory, make_response
from flask_socketio import SocketIO

try:
    from .project_manager import ProjectManager
except ImportError:
    from project_manager import ProjectManager

logging.getLogger("geventwebsocket.handler").setLevel(logging.WARN)
# 构建模板和静态文件目录路径
current_dir = os.path.dirname(os.path.abspath(__file__))
template_dir = os.path.join(current_dir, "..", "templates")
static_dir = os.path.join(current_dir, "..", "static")
# 实例化Flask应用并指定模板和静态文件目录
app = Flask(__name__, template_folder=template_dir, static_folder=static_dir)
socketio = SocketIO(app, cors_allowed_origins="*")

# 默认端口为5006
PORT = 5006

# xterm 配置 - 改为字典，支持多项目
terminals = (
    {}
)  # 格式: {project_id: {"fd": fd, "child_pid": pid, "last_activity": timestamp, "sid": session_id}}
terminal_cleanup_thread = None

@app.route("/aiterm/xterm.html")
def xterm_html():
    """终端页面"""
    response = app.send_static_file("xterm.html")
    # 添加权限策略头，允许 Clipboard API
    response.headers['Permissions-Policy'] = 'clipboard-read=*, clipboard-write=*'
    return response

@app.route("/aiterm/static/<path:filename>")
def xterm_static_files(filename):
    """提供带aiterm前缀的静态资源文件"""
    return send_from_directory(static_dir, filename)

def cleanup_terminal(project_id):
    """清理指定项目的终端进程"""
    terminal = terminals.get(project_id)

    if not terminal:
        return

    try:
        # 关闭 fd
        if terminal.get("fd"):
            try:
                os.close(terminal["fd"])
            except OSError:
                pass

        # 终止子进程
        if terminal.get("child_pid"):
            try:
                # 先尝试 SIGTERM，给进程优雅退出的机会
                os.kill(terminal["child_pid"], signal.SIGTERM)
                gevent.sleep(0.1)
                # 如果还存在，强制 SIGKILL
                try:
                    os.kill(terminal["child_pid"], signal.SIGKILL)
                except ProcessLookupError:
                    pass
            except ProcessLookupError:
                pass

        # 删除旧版 wrapper 脚本或初始化脚本
        for key in ("wrapper_script", "init_script"):
            script_path = terminal.get(key)
            if script_path and os.path.exists(script_path):
                try:
                    os.remove(script_path)
                except OSError:
                    pass

        terminal.pop("ai_prompt_buffer", None)

        # 从字典中移除
        del terminals[project_id]
        logging.info(f"终端已清理，项目ID: {project_id}")
    except Exception as e:
        logging.error(f"清理终端失败，项目ID: {project_id}, 错误: {e}")


def cleanup_idle_terminals():
    """后台线程：定期清理空闲超时的终端"""
    IDLE_TIMEOUT = 30 * 60  # 30分钟无活动则清理

    while True:
        try:
            gevent.sleep(60)  # 每分钟检查一次

            current_time = time.time()
            to_cleanup = []

            for project_id, terminal in list(terminals.items()):
                last_activity = terminal.get("last_activity", 0)
                if current_time - last_activity > IDLE_TIMEOUT:
                    to_cleanup.append(project_id)
                    logging.info(f"终端空闲超时，准备清理，项目ID: {project_id}")

            # 清理超时的终端
            for project_id in to_cleanup:
                cleanup_terminal(project_id)

        except Exception as e:
            logging.error(f"清理空闲终端时出错: {e}")


def set_winsize(fd, row, col, xpix=0, ypix=0):
    """设置终端窗口大小"""
    winsize = struct.pack("HHHH", row, col, xpix, ypix)
    fcntl.ioctl(fd, termios.TIOCSWINSZ, winsize)


def build_ai_hook_script(script_dir, work_dir: str = ""):
    """生成在 shell 中注册 /ai 调用的脚本"""
    return f"""
# AI 命令 hook 初始化
if [[ "${{__AI_WRAPPER_READY:-0}}" != "1" ]]; then
    __AI_SCRIPT_DIR="{script_dir}"
    __ai_in_debug=0

    __ai_debug_hook() {{
        if [[ $__ai_in_debug -ne 0 ]]; then
            return
        fi

        local pending_cmd="$BASH_COMMAND"
        if [[ "$pending_cmd" == "/ai" || "$pending_cmd" == "/ai "* ]]; then
            __ai_in_debug=1

            local query="${{pending_cmd#/ai}}"
            query="${{query##[[:space:]]}}"

            printf '\\n'
            if [[ -z "$query" ]]; then
                printf '# 使用方法: /ai <你想执行的操作>\\n'
                printf '# 例如: /ai 查询主板型号\\n'
            else
                local result
                result=$(python3 "$__AI_SCRIPT_DIR/ai_command_helper.py" "$query" "{work_dir}" 2>&1)
                local exit_code=$?
                if [[ $exit_code -eq 0 ]]; then
                    local inline_cmd="$result"
                    inline_cmd="${{inline_cmd//$'\\r'/}}"
                    inline_cmd="${{inline_cmd//$'\\n'/ && }}"
                    inline_cmd="${{inline_cmd%% && }}"
                    inline_cmd="${{inline_cmd## }}"
                    inline_cmd="${{inline_cmd%% }}"

                    printf '__AI_PROMPT__%s' "$inline_cmd"
                else
                    printf '# AI 生成命令失败: %s\\n' "$result"
                fi
            fi

            history -d $((HISTCMD)) 2>/dev/null || history -d $((HISTCMD-1)) 2>/dev/null || true

            __ai_in_debug=0
            kill -SIGINT $$ >/dev/null 2>&1 || true
        fi
    }}

    trap '__ai_debug_hook' DEBUG
    export __AI_WRAPPER_READY=1
fi
"""


def read_and_forward_pty_output(project_id, fd):
    """读取并转发 PTY 输出"""
    max_read_bytes = 1024 * 20
    try:
        while True:
            socketio.sleep(0.01)
            terminal = terminals.get(project_id)

            if not terminal or terminal.get("fd") != fd:
                # 终端已关闭或被替换
                logging.info(f"终端输出任务退出，项目ID: {project_id}")
                break

            timeout_sec = 0
            (data_ready, _, _) = select.select([fd], [], [], timeout_sec)
            if data_ready:
                output_bytes = os.read(fd, max_read_bytes)
                if not output_bytes:
                    logging.info(f"PTY 读取到 EOF，终止输出循环，项目ID: {project_id}")
                    break
                output = output_bytes.decode(errors="ignore")

                buffer = terminal.get("ai_prompt_buffer", "") + output
                terminal["ai_prompt_buffer"] = ""
                forward_chunks = []

                while True:
                    newline_index = buffer.find("\n")
                    if newline_index == -1:
                        break

                    line = buffer[:newline_index]
                    buffer = buffer[newline_index + 1 :]
                    stripped = line.rstrip("\r")

                    if stripped.startswith(AI_PROMPT_MARKER):
                        command_text = stripped[len(AI_PROMPT_MARKER) :]
                        if command_text:
                            try:
                                os.write(fd, command_text.encode())
                            except OSError as write_error:
                                logging.error(
                                    f"向 PTY 写入 AI 命令失败，项目ID: {project_id}, 错误: {write_error}"
                                )
                    elif stripped == "^C":
                        continue
                    else:
                        forward_chunks.append(line + "\n")

                if buffer:
                    candidate = buffer.rstrip("\r")
                    if AI_PROMPT_MARKER.startswith(candidate):
                        terminal["ai_prompt_buffer"] = buffer
                    else:
                        forward_chunks.append(buffer)
                        terminal["ai_prompt_buffer"] = ""
                else:
                    terminal["ai_prompt_buffer"] = ""

                if forward_chunks:
                    socketio.emit(
                        "pty-output",
                        {"output": "".join(forward_chunks), "project_id": project_id},
                        namespace="/aiterm/pty",
                    )
    except OSError as e:
        logging.error(f"读取 PTY 输出失败，项目ID: {project_id}, 错误: {e}")
    finally:
        current_terminal = terminals.get(project_id)
        if current_terminal and current_terminal.get("fd") == fd:
            cleanup_terminal(project_id)


def pty_input(data):
    """接收浏览器输入并写入 PTY"""
    project_id = data.get("project_id")
    if not project_id:
        logging.warning("pty-input: 缺少 project_id")
        return

    terminal = terminals.get(project_id)

    if not terminal or not terminal.get("fd"):
        return

    try:
        # 更新最后活动时间
        terminal["last_activity"] = time.time()

        user_input = data["input"]

        # 直接写入 PTY，/ai 命令由 bash wrapper 处理
        os.write(terminal["fd"], user_input.encode())

    except OSError as e:
        logging.error(f"写入 PTY 失败，项目ID: {project_id}, 错误: {e}")


def resize(data):
    """调整终端窗口大小"""
    project_id = data.get("project_id")
    if not project_id:
        logging.warning("resize: 缺少 project_id")
        return

    terminal = terminals.get(project_id)

    if terminal and terminal.get("fd"):
        try:
            # 更新最后活动时间
            terminal["last_activity"] = time.time()
            set_winsize(terminal["fd"], data["rows"], data["cols"])
        except Exception as e:
            logging.error(f"调整终端大小失败，项目ID: {project_id}, 错误: {e}")


def pty_connect():
    """客户端连接到 PTY"""
    # 获取查询参数中的 project_id
    project_id = request.args.get("project_id")

    if not project_id:
        logging.error("连接失败：缺少 project_id 参数")
        return False

    logging.info(f"新客户端连接到 xterm，项目ID: {project_id}")

    # 获取项目信息,每次重新加载项目文件
    project_manager = ProjectManager()
    project = project_manager.get_project(project_id)
    if not project:
        logging.error(f"连接失败：项目不存在，项目ID: {project_id}")
        return False

    work_dir = project.work_dir
    if not os.path.exists(work_dir):
        logging.error(
            f"连接失败：项目工作目录不存在，项目ID: {project_id}, 目录: {work_dir}"
        )
        return False

    # 检查是否已存在该项目的终端
    if project_id in terminals:
        existing = terminals[project_id]
        logging.info(
            f"项目 {project_id} 已有终端进程，PID: {existing.get('child_pid')}"
        )

        existing.setdefault("ai_prompt_buffer", "")

        # 如果之前的终端尚未注入 AI hook，则补注
        if not existing.get("ai_ready"):
            ai_script = (
                "\n" + build_ai_hook_script(os.path.dirname(__file__), work_dir) + "\n"
            )
            try:
                os.write(existing["fd"], ai_script.encode())
                existing["ai_ready"] = True
                logging.info(f"旧终端已注入 AI hook，项目ID: {project_id}")
            except OSError as e:
                logging.error(
                    f"向旧终端注入 AI hook 失败，项目ID: {project_id}, 错误: {e}"
                )

        # 可以选择复用或者关闭旧的，这里选择复用
        return True

    # 为当前终端生成初始化脚本
    try:
        init_fd, init_path = tempfile.mkstemp(prefix="ai_shell_init_", suffix=".sh")
        init_content = "#!/usr/bin/env bash\n" + build_ai_hook_script(
            os.path.dirname(__file__), work_dir
        )
        os.write(init_fd, init_content.encode())
        os.close(init_fd)
        os.chmod(init_path, 0o600)
    except Exception as e:
        logging.error(f"创建 AI 初始化脚本失败，项目ID: {project_id}, 错误: {e}")
        return False

    # 创建附加到 PTY 的子进程
    (child_pid, fd) = pty.fork()
    if child_pid == 0:
        # 子进程 - 切换到项目工作目录并执行 shell
        try:
            # 切换工作目录
            os.chdir(work_dir)

            # 设置环境变量
            env = os.environ.copy()
            # 清理可能干扰的调试器环境变量
            for key in list(env.keys()):
                if "PYDEVD" in key or "PYTHONBREAKPOINT" in key:
                    del env[key]

            # 根据项目的 provider 设置 Anthropic 环境变量
            try:
                from config import Config

                provider_key = (
                    project.provider
                    if hasattr(project, "provider") and project.provider
                    else "local"
                )
                provider_config = Config.PROVIDERS.get(provider_key, {})

                if provider_config:
                    env["ANTHROPIC_BASE_URL"] = provider_config.get("base_url", "")
                    env["ANTHROPIC_AUTH_TOKEN"] = provider_config.get("auth_token", "")
                    env["ANTHROPIC_MODEL"] = provider_config.get("model", "")
                    env["ANTHROPIC_API_KEY"] = provider_config.get("api_key", "")
                    env["CLAUDE_CODE_MAX_OUTPUT_TOKENS"] = provider_config.get(
                        "max_tokens", "15000"
                    )
                    env["API_TIMEOUT_MS"] = "300000"  # 10分钟超时
                    env["AI_PROVIDER"] = provider_key  # 供 ai_command_helper.py 使用

                    logging.info(
                        f"终端环境变量已配置，provider: {provider_key}, model: {provider_config.get('model', 'N/A')}"
                    )
            except Exception as e:
                logging.warning(f"设置 provider 环境变量失败: {e}")

            # 设置自定义提示符 - 只显示项目名称前6个字符
            project_name_short = (
                project.name[:6] + ".." if len(project.name) > 6 else project.name
            )
            env["PS1"] = f"[{project_name_short}] \\W $ "

            # 确保终端行为与交互式 shell 一致
            env.setdefault("TERM", "xterm-256color")

            # 使用自定义 rcfile 初始化 shell
            os.execvpe(
                "bash", ["bash", "--noprofile", "--rcfile", init_path, "-i"], env
            )

        except Exception as e:
            try:
                os.remove(init_path)
            except OSError:
                pass
            # 如果启动失败，打印错误并退出
            print(f"子进程启动失败: {e}", file=sys.stderr)
            os._exit(1)
    else:
        # 父进程
        # 获取 session id（用于断开连接时清理）
        try:
            from flask import request as flask_request

            sid = flask_request.sid if hasattr(flask_request, "sid") else None
        except:
            sid = None

        terminals[project_id] = {
            "fd": fd,
            "child_pid": child_pid,
            "project_name": project.name,
            "work_dir": work_dir,
            "last_activity": time.time(),
            "sid": sid,
            "init_script": init_path,
            "ai_ready": True,
            "ai_prompt_buffer": "",
        }

        set_winsize(fd, 50, 50)

        logging.info(
            f"xterm 子进程启动成功 - 项目: {project.name}, PID: {child_pid}, FD: {fd}, 工作目录: {work_dir}"
        )

        # 启动后台任务读取 PTY 输出
        socketio.start_background_task(
            target=read_and_forward_pty_output, project_id=project_id, fd=fd
        )

        # 发送一个换行符触发 bash 显示提示符
        # 延迟一点，等待 bash 完全启动
        import time as time_module

        time_module.sleep(0.1)
        try:
            os.write(fd, b"\n")
        except:
            pass

        # 启动清理线程（只启动一次）
        global terminal_cleanup_thread
        if terminal_cleanup_thread is None:
            cleanup_thread = threading.Thread(
                target=cleanup_idle_terminals, daemon=True
            )
            cleanup_thread.start()
            terminal_cleanup_thread = cleanup_thread
            logging.info("终端清理线程已启动")

        return True


def pty_disconnect():
    """客户端断开连接"""
    # WebSocket 断开时，标记所有终端的最后活动时间
    # 让清理线程在空闲超时后处理
    # 这样如果用户快速重连，可以复用终端
    logging.info("客户端断开 xterm 连接")


def pty_close(data):
    """关闭指定项目的终端"""
    project_id = data.get("project_id")
    if not project_id:
        return

    logging.info(f"收到关闭终端请求，项目ID: {project_id}")
    cleanup_terminal(project_id)


def api_list_terminals():
    """获取所有活动的终端信息"""
    result = []

    for project_id, terminal in terminals.items():
        result.append(
            {
                "project_id": project_id,
                "project_name": terminal.get("project_name"),
                "work_dir": terminal.get("work_dir"),
                "child_pid": terminal.get("child_pid"),
                "last_activity": terminal.get("last_activity"),
                "idle_seconds": int(time.time() - terminal.get("last_activity", 0)),
            }
        )

    return {"terminals": result, "total": len(result)}


def api_close_terminal(project_id):
    """关闭指定项目的终端"""
    try:
        cleanup_terminal(project_id)
        return {"success": True, "message": f"终端已关闭，项目ID: {project_id}"}
    except Exception as e:
        return {"success": False, "message": str(e)}


def kill_process_on_port(port):
    """杀死占用指定端口的进程"""
    import subprocess
    import signal

    try:
        # 查找占用端口的进程
        result = subprocess.run(
            ['lsof', '-ti', f':{port}'],
            capture_output=True,
            text=True,
            timeout=5
        )

        if result.returncode == 0 and result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                if pid.strip():
                    try:
                        pid_int = int(pid.strip())
                        print(f"🔪 杀死占用端口 {port} 的进程 PID: {pid_int}")
                        os.kill(pid_int, signal.SIGTERM)
                        # 等待一下让进程优雅退出
                        import time
                        time.sleep(0.5)
                        # 如果还没退出，强制杀死
                        try:
                            os.kill(pid_int, signal.SIGKILL)
                        except ProcessLookupError:
                            pass  # 进程已经退出
                        print(f"✅ 成功杀死进程 {pid_int}")
                    except (ValueError, ProcessLookupError) as e:
                        print(f"⚠️ 无法杀死进程 {pid}: {e}")
            return True
        else:
            print(f"ℹ️ 端口 {port} 没有被占用")
            return False

    except subprocess.TimeoutExpired:
        print(f"⚠️ 查找端口 {port} 占用进程超时")
        return False
    except FileNotFoundError:
        # lsof命令不存在，尝试使用netstat
        try:
            result = subprocess.run(
                ['netstat', '-tlnp'],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if f':{port} ' in line and 'LISTEN' in line:
                        # 提取PID
                        parts = line.split()
                        if len(parts) >= 7:
                            pid_info = parts[6]  # 格式通常是 pid/program_name
                            if '/' in pid_info:
                                pid = pid_info.split('/')[0]
                                try:
                                    pid_int = int(pid)
                                    print(f"🔪 杀死占用端口 {port} 的进程 PID: {pid_int}")
                                    os.kill(pid_int, signal.SIGTERM)
                                    time.sleep(0.5)
                                    try:
                                        os.kill(pid_int, signal.SIGKILL)
                                    except ProcessLookupError:
                                        pass
                                    print(f"✅ 成功杀死进程 {pid_int}")
                                    return True
                                except (ValueError, ProcessLookupError) as e:
                                    print(f"⚠️ 无法杀死进程 {pid}: {e}")
            return False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print(f"⚠️ 无法查找端口 {port} 占用进程")
            return False
    except Exception as e:
        print(f"⚠️ 查找端口占用进程时出错: {e}")
        return False

def start_server(host="0.0.0.0", port=None):
    """启动xterm服务器"""
    global PORT
    if port:
        PORT = port

    print("=" * 60)
    print(f"🌐 AI终端服务地址: http://{host}:{PORT}")
    print("=" * 60)

    try:
        # 启用地址重用，避免debug模式重启时的端口冲突
        socketio.run(app, host=host, port=PORT, allow_unsafe_werkzeug=True)
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"⚠️ 端口 {PORT} 已被占用，尝试杀死占用进程...")

            # 杀死占用端口的进程
            if kill_process_on_port(PORT):
                print(f"🔄 重新尝试启动xterm服务...")
                import time
                time.sleep(1)  # 等待端口释放
                try:
                    socketio.run(app, host=host, port=PORT, allow_unsafe_werkzeug=True)
                    print(f"✅ 成功启动xterm服务在端口 {PORT}")
                except OSError as retry_e:
                    print(f"❌ 重试后仍然失败: {retry_e}")
                    raise retry_e
            else:
                print(f"❌ 无法清理端口 {PORT}，原始错误: {e}")
                raise e
        else:
            raise e


# 注册xterm socketio事件处理器
socketio.on("pty-input", namespace="/aiterm/pty")(pty_input)
socketio.on("resize", namespace="/aiterm/pty")(resize)
socketio.on("connect", namespace="/aiterm/pty")(pty_connect)
socketio.on("disconnect", namespace="/aiterm/pty")(pty_disconnect)
socketio.on("close", namespace="/aiterm/pty")(pty_close)

AI_PROMPT_MARKER = "__AI_PROMPT__"

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="AI全栈助手系统终端服务")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=5006, help="服务器端口")

    args = parser.parse_args()

    # 初始化应用
    # 注意：这里需要实际初始化project_manager，暂时留空
    # init_xterm_app()

    start_server(args.host, args.port)
