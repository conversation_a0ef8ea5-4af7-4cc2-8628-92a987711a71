#!/usr/bin/env python3
"""
AI全栈助手系统启动脚本
"""

import os
import sys
import argparse
import logging
import threading
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# 
logging.getLogger('werkzeug').setLevel(logging.INFO)

from web_app import init_app
from config import Config
# 导入日志清理模块
from log_cleanup import start_log_cleanup_thread
# 导入xterm模块
from src.xterm import start_server as start_xterm_server

def start_xterm_service(host, port):
    """在独立线程中启动xterm服务"""
    try:
        start_xterm_server(host, port)
    except Exception as e:
        print(f"⚠️ xterm服务启动失败: {e}")
        # 在debug模式下，这个错误不应该阻止主应用启动
        if "Address already in use" in str(e):
            print("💡 这通常是debug模式重启导致的端口冲突，主应用仍可正常使用")

def main():
    parser = argparse.ArgumentParser(description='AI全栈助手系统')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=5005, help='主应用端口')
    parser.add_argument('--xterm-port', type=int, default=5006, help='终端服务端口')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    #parser.add_argument('--data-dir', default='./data', help='数据存储目录')
    
    args = parser.parse_args()
    
    # 确保数据目录存在
    os.makedirs(Config.DATA_DIR, exist_ok=True)
    #args.debug = True
    
    print("=" * 60)
    print("🚀 AI全栈助手系统启动中...")
    print("=" * 60)
    print(f"📁 数据目录: {os.path.abspath(Config.DATA_DIR)}")
    print(f"🌐 主应用地址: http://{args.host}:{args.port}")
    print(f"🌐 终端服务地址: http://{args.host}:{args.xterm_port}")
    print(f"🔧 调试模式: {'开启' if args.debug else '关闭'}")
    print("=" * 60)
        
    # 初始化应用
    app = init_app(args.port, args.xterm_port)
    
    # 检查是否启用日志清理功能
    enable_log_cleanup = os.getenv('ENABLE_LOG_CLEANUP', 'true').lower() == 'true'
    if enable_log_cleanup:
        start_log_cleanup_thread()
        print("🧹 日志清理线程已启动")
    else:
        print("🚫 日志清理线程已禁用")
    
    print("✅ 应用初始化完成")
    print("=" * 60)
    
    # 在独立线程中启动xterm服务
    xterm_thread = threading.Thread(
        target=start_xterm_service, 
        args=(args.host, args.xterm_port),
        daemon=True
    )
    xterm_thread.start()
    
    # 启动主Flask应用
    app.run(
        host=args.host,
        port=args.port,
        debug=args.debug or Config.DEBUG_MODE,
    )
   

if __name__ == '__main__':
    main()